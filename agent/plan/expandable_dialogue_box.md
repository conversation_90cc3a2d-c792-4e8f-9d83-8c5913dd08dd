# Expandable Agent Dialogue Box Implementation Plan

## Background

The user wants to make the agent dialogue box expandable, similar to the existing sliding animations used in the conversation history panel and status bar. When clicked, the dialogue box should expand vertically downward until it reaches the bottom of the usable screen (above the agent toolbar).

## Analysis / Key Challenges

### Current Implementation Analysis
- **AgentDialogueBox**: Currently a fixed-height (180.dp) Card component in MainScreen.kt
- **Layout Structure**: Positioned in a Column with fixed padding and spacing
- **Animation Patterns**: Existing sliding animations use `AnimatedVisibility` with `slideInHorizontally`/`slideOutHorizontally`
- **Constraints**: Must expand down to agent toolbar, respecting existing layout boundaries

### Technical Considerations
1. **Animation Pattern**: Need vertical expansion instead of horizontal sliding
2. **Layout Constraints**: Must calculate available space dynamically
3. **State Management**: Need to track expanded/collapsed state
4. **Click Handling**: Add click detection to current dialogue box
5. **Content Adaptation**: Text content should adapt to expanded space
6. **Consistency**: Follow existing animation patterns (300ms duration, similar easing)

### Existing Animation Reference
From AgentProcessPanel.kt and AgentPanels.kt:
- Uses `AnimatedVisibility` with `slideInHorizontally`/`slideOutHorizontally`
- 300ms duration with `tween` animation spec
- Toggle state managed by parent component
- Consistent styling with rounded corners and elevation

## Task Breakdown

### [IN PROGRESS] Task 1: Create Expandable Dialogue Box Component
- Extract current AgentDialogueBox logic into expandable version
- Add click detection and state management
- Implement vertical expansion animation using `animateContentSize()`
- Calculate maximum expansion height based on available space
- Ensure content adapts properly to expanded state

### [ ] Task 2: Integrate with MainScreen Layout
- Update MainScreen.kt to use new expandable component
- Pass necessary callbacks and state
- Ensure proper spacing and layout constraints
- Test interaction with existing UI elements

### [ ] Task 3: Styling and Polish
- Match existing animation timing and easing
- Ensure consistent visual styling with other panels
- Add appropriate elevation and shadow effects
- Test on different screen sizes

## Success Criteria

1. **Functional Expansion**: Dialogue box expands vertically when clicked
2. **Proper Boundaries**: Expansion stops at agent toolbar boundary
3. **Smooth Animation**: 300ms animation matching existing patterns
4. **Content Adaptation**: Text content utilizes expanded space effectively
5. **Visual Consistency**: Styling matches existing panel components
6. **Responsive Design**: Works across different screen sizes

## Implementation Notes

- Use `animateContentSize()` for smooth height transitions
- Calculate `maxHeight` using `BoxWithConstraints` to respect toolbar boundary
- Follow existing animation constants from `AgentAnimations` object
- Maintain existing visual styling (colors, borders, elevation)
- Ensure accessibility with proper content descriptions
