import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.example.voxmanifestorapp.R
import com.example.voxmanifestorapp.data.MAX_WISH_SLOTS
import com.example.voxmanifestorapp.data.Manifestation
import com.example.voxmanifestorapp.data.RecognitionState
import com.example.voxmanifestorapp.ui.agent.DialogueState
import com.example.voxmanifestorapp.ui.agent.DisplayState
// TODO: Re-enable affirmation functionality post-MVP
// import com.example.voxmanifestorapp.ui.agent.affirmations.AffirmationOverlay
import com.example.voxmanifestorapp.ui.agent.components.AgentProcessPanel
import com.example.voxmanifestorapp.ui.agent.components.AgentToolbar
import com.example.voxmanifestorapp.ui.agent.components.ConversationHistoryPanel
import com.example.voxmanifestorapp.ui.agent.components.GenieAvatar
import com.example.voxmanifestorapp.ui.agent.components.ModeStatusBar
import com.example.voxmanifestorapp.ui.agent.components.StatusPanel
import com.example.voxmanifestorapp.ui.agent.timer.TimerIntent
import com.example.voxmanifestorapp.ui.agent.voice.VoiceCommandEntity
import com.example.voxmanifestorapp.ui.basevox.VoiceManagedViewModel
import com.example.voxmanifestorapp.ui.statuslog.StatusMessageService

@Composable
fun AgentDialogueBox(
        latestUtterance: String?,
        recognitionState: RecognitionState,
        dialogueState: DialogueState,
        isExpanded: Boolean,
        onToggleExpanded: () -> Unit,
        maxHeight: Dp,
        modifier: Modifier = Modifier
) {
  val borderBlue = Color(0xFF3F51B5) // Indigo border for agent panel (same as Genie panel)
  val accentGold = Color(0xFFD4AF37) // Rich gold for accents
  val deepPurple = Color(0xFF4A2545) // Deep purple for contrast
  val backgroundBeige = Color(0xFFF2EAD3) // Warm beige background
  val lightBeige = Color(0xFFF8F4E8) // Lighter beige for transparency effect

  // Calculate height based on expansion state
  val targetHeight = if (isExpanded) maxHeight else 180.dp

  // Single border with purple color matching Genie panel
  Card(
          modifier =
                  modifier.fillMaxWidth()
                          .height(targetHeight)
                          .animateContentSize(
                                  animationSpec = tween(durationMillis = 300)
                          )
                          .clickable { onToggleExpanded() },
          colors =
                  CardDefaults.cardColors(
                          containerColor = Color(0xFFE8F4FD).copy(alpha = 0.9f)
                  ), // Pale blue background with potential for gradient
          border = BorderStroke(1.dp, borderBlue), // Thin purple border matching Genie panel
          shape = RoundedCornerShape(4.dp), // Smaller curves on corners
          elevation =
                  CardDefaults.cardElevation(defaultElevation = 12.dp) // Strong shadow for depth
  ) {
    Box(modifier = Modifier.fillMaxSize()) {
      // Animated GenieAvatar anchored to top-left corner
      Box(
              modifier =
                      Modifier.size(72.dp)
                              .background(
                                      color = backgroundBeige,
                                      shape =
                                              RoundedCornerShape(
                                                      3.dp
                                              ) // Smaller rounded avatar container
                              )
                              .border(
                                      width = 1.dp,
                                      color = borderBlue, // Purple border matching Genie panel
                                      shape = RoundedCornerShape(3.dp)
                              ),
              contentAlignment = Alignment.Center
      ) {
        // Animated GenieAvatar (bigger size)
        GenieAvatar(
                recognitionState = recognitionState,
                dialogueState = dialogueState,
                size = 64.dp // Bigger avatar
        )
      }

      // Agent utterance text with proper wrapping around avatar
      Text(
              text = latestUtterance ?: "Genie is ready to assist...",
              modifier =
                      Modifier.fillMaxSize()
                              .padding(
                                      start = 80.dp, // Start after the 72.dp avatar
                                      top = 8.dp, // Reduced top padding
                                      end = 16.dp,
                                      bottom = 8.dp // Reduced bottom padding
                              ),
              style =
                      androidx.compose.ui.text.TextStyle(
                              fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                              fontWeight = FontWeight.Medium,
                              fontSize = 15.sp, // Slightly larger text
                              color = deepPurple
                      ),
              maxLines = if (isExpanded) Int.MAX_VALUE else 8, // Unlimited lines when expanded
              overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
      )
    }
  }
}

@Composable
fun MainScreen(
        onRequestPermission: () -> Unit,
        viewModel: MainViewModel,
        voiceModel: VoiceManagedViewModel,
        statusMessageService: StatusMessageService,
        onDetailsOpen: (Int) -> Unit
) {
  // The Composable needs to observe the values emitted by the stream
  // and react by redrawing. This requires the specific mechanism provided by collectAsState()
  // to bridge the Flow world with the Compose State world.

  // load state variable from manifestation view model
  val manifestations by viewModel.allManifestations.collectAsState(initial = emptyList())

  // load state variables from voice view model
  val recognitionState by voiceModel.recognitionState.collectAsState()

  val commandState by viewModel.commandState.collectAsState()
  val dialogueState by viewModel.dialogueState.collectAsState()

  // Add this line to observe conversation type
  val conversationType by viewModel.conversationType.collectAsState()

          // TODO: Re-enable affirmation functionality post-MVP
        // agent overlays for affirmations.
        val agentDisplayState by viewModel.agentDisplayState.collectAsState()

  // For agent process panel
  val conversationHistory by viewModel.conversationHistory.collectAsState()
  val currentPlan by viewModel.currentPlan.collectAsState()
  val currentAction by viewModel.currentAction.collectAsState()

  // Get coreLoopState from viewModel
  val coreLoopState by viewModel.coreLoopState.collectAsState()

  // For Mode Status Bar
  val checkInState by viewModel.checkInState.collectAsState()

  // Session history
  val sessionInfoList by viewModel.sessionInfoList.collectAsState()
  val sessionEntries by viewModel.sessionEntries.collectAsState()
  val viewingSessionId by viewModel.viewingSessionId.collectAsState()

  val rawTextState by voiceModel.rawTextState.collectAsState()
  val statusMessages by statusMessageService.statusMessages.collectAsState()

  // Latest agent utterance for display
  val latestAgentUtterance by viewModel.latestAgentUtterance.collectAsState()

  // vars that deal with selection of specific wish slots
  // todo: what is this variable used for ?
  var wishSlot = Array<Manifestation?>(MAX_WISH_SLOTS) { null }
  // we use the mainScreenState object as an API into the main screen, controllable by the agent.
  val selectedSlot by viewModel.selectedSlot.collectAsState()

  // Agent process panel state
  var isAgentPanelExpanded by remember { mutableStateOf(false) }
  // Add state for status panel
  var isStatusPanelExpanded by remember { mutableStateOf(false) }
  // Add state for conversation history panel
  var isConversationPanelExpanded by remember { mutableStateOf(false) }

  // color scheme
  val backgroundBeige = Color(0xFFF2EAD3) // Warm beige background
  val borderBlue = Color(0xFF3F51B5) // Indigo border for agent panel

  /** Mode Status Bar and Agent Toolbar Height Configs */
  val toolbarHeight = 48.dp
  val statusBarHeight = 48.dp
  val barSpacing = 0.dp
  val totalBottomHeight = toolbarHeight + statusBarHeight + barSpacing
  val recogZoneBottomPadding = totalBottomHeight + 16.dp // Add extra padding for visual separation

  manifestations.forEach { wish ->
    // assign the manifestation to its corresponding wish slot
    wishSlot[wish.slot] = wish
  }

  // Box for overall layout and overlays
  Box(modifier = Modifier.fillMaxSize().background(backgroundBeige)) {

    // Title Band - Positioned absolutely at top like bottom status bars
    VoxManifestorTitle(
            modifier =
                    Modifier.align(Alignment.TopCenter)
                            .fillMaxWidth()
                            .statusBarsPadding() // Add proper top padding to avoid system status
            // bar
            )

    // Main content Column (excluding the absolutely positioned toolbar and title)
    Column(
            modifier =
                    Modifier.fillMaxSize() // Make column fill the screen
                            .navigationBarsPadding() // Handles system bars first
                            // Add padding at the bottom so content doesn't overlap with toolbar
                            .padding(bottom = totalBottomHeight)
                            .padding(horizontal = 16.dp) // Horizontal padding
                            .padding(top = 80.dp), // Top padding to account for title band
            horizontalAlignment = Alignment.CenterHorizontally // Keep original alignment
    ) {
      // Agent Dialogue Box - Heads-up display style
      AgentDialogueBox(
              latestUtterance = latestAgentUtterance,
              recognitionState = recognitionState,
              dialogueState = dialogueState,
              modifier =
                      Modifier.padding(
                              top = 24.dp,
                              bottom = 8.dp
                      ) // Increased top padding to drop dialogue box down
      )

      // Spacer between dialogue box and manifestations
      Spacer(modifier = Modifier.height(16.dp))

      // Small breathing space before the wish list
      // (Remove large weighted spacer to avoid excessive gap)

      /* manifestation segment */
      Card(
              modifier = Modifier.fillMaxWidth().height((MAX_WISH_SLOTS * 70).dp),
              colors =
                      CardDefaults.cardColors(
                              containerColor = Color.Transparent
                      ), // Transparent background
              border = BorderStroke(1.dp, borderBlue), // Purple border matching dialogue panel
              shape = RoundedCornerShape(4.dp), // Slight curves to match dialogue panel
              elevation = CardDefaults.cardElevation(defaultElevation = 8.dp) // Strong shadow
      ) {
        LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(0.dp) // Remove spacing between cards
        ) {
          items(MAX_WISH_SLOTS) { index ->
            wishSlot[index]?.let {
              ManifestationItem(
                      manifestation = it,
                      onSelect = { slot, isSelected -> viewModel.toggleSlotSelection(slot) },
                      isSelected = selectedSlot == index
              )
            }
                    ?: EmptyManifestationItem()
          }
        }
      }

      // Reserve fixed space for speech recognition box (70dp) + margin (16dp)
      Spacer(modifier = Modifier.height(86.dp))
    } // End Main Content Column

    // Bottom UI Elements Container
    Box(
            modifier =
                    Modifier.align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .navigationBarsPadding() // Apply padding once at container level
    ) {
      // Mode Status Bar - Position at bottom
      Card(
              modifier = Modifier.align(Alignment.BottomCenter).fillMaxWidth(),
              colors = CardDefaults.cardColors(containerColor = Color(0xFFE6D9B8)),
              elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
              border = BorderStroke(1.dp, Color(0xFFD4C4A8)),
              shape = RectangleShape
      ) {
        ModeStatusBar(
                currentMode = "Check-In",
                timerState = checkInState.timerState,
                onAddTime = { viewModel.handleTimerIntent(TimerIntent.AddTime) },
                onSubtractTime = { viewModel.handleTimerIntent(TimerIntent.SubtractTime) },
                onStop = { viewModel.handleTimerIntent(TimerIntent.Stop) },
                modifier =
                        Modifier.fillMaxWidth().height(statusBarHeight).padding(horizontal = 16.dp)
        )
      }

      // Agent Toolbar - Position above ModeStatusBar
      Card(
              modifier =
                      Modifier.align(Alignment.BottomCenter)
                              .fillMaxWidth()
                              .padding(
                                      bottom = statusBarHeight + barSpacing
                              ), // Position above ModeStatusBar
              shape =
                      RoundedCornerShape(
                              topStart = 12.dp,
                              topEnd = 12.dp,
                              bottomStart = 0.dp,
                              bottomEnd = 0.dp
                      ),
              border = BorderStroke(1.dp, Color(0xFF2C3E50)),
              colors = CardDefaults.cardColors(containerColor = Color(0xFF394A59)),
              elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
      ) {
        AgentToolbar(
                recognitionState = recognitionState,
                dialogueState = dialogueState,
                onMicClick = { voiceModel.handleMicrophoneClick(onRequestPermission) },
                onInterrupt = { viewModel.requestInterrupt() },
                onToggleConversation = { viewModel.toggleConversation() },
                onSendResponse = { viewModel.sendResponse() },
                modifier =
                        Modifier.fillMaxWidth()
                                .height(toolbarHeight)
                                .padding(horizontal = 8.dp, vertical = 0.dp)
        )
      }
    }

    // Speech recognition box positioned above the toolbar
    VoxRecogZone(
            recognitionState = recognitionState,
            commandState = commandState,
            rawTextState = rawTextState.first,
            modifier =
                    Modifier.align(Alignment.BottomCenter)
                            .navigationBarsPadding()
                            .padding(bottom = recogZoneBottomPadding, start = 16.dp, end = 16.dp)
                            .fillMaxWidth()
    )

    // Status Panel - Right side
    StatusPanel(
            statusMessages = statusMessages,
            isExpanded = isStatusPanelExpanded,
            onToggleExpanded = { isStatusPanelExpanded = !isStatusPanelExpanded },
            modifier =
                    Modifier.align(Alignment.BottomEnd)
                            .navigationBarsPadding()
                            .padding(bottom = 64.dp)
                            .zIndex(10f),
            height = 350.dp,
            tabPosition = 0.27f // Position tab closer to the top (30% down from top)
    )

    // Conversation History Panel - Left side
    if (isConversationPanelExpanded) {
      // Add a FloatingActionButton to reload session data
      FloatingActionButton(
              onClick = { viewModel.loadAllSessionInfo() },
              modifier =
                      Modifier.align(Alignment.BottomStart)
                              .padding(start = 16.dp, bottom = 128.dp)
                              .zIndex(20f),
              containerColor = Color(0xFF2196F3)
      ) {
        Icon(
                imageVector = Icons.Default.KeyboardArrowUp,
                contentDescription = "Reload Sessions",
                tint = Color.White
        )
      }
    }

    ConversationHistoryPanel(
            conversationHistory =
                    if (viewingSessionId == null) conversationHistory else sessionEntries,
            isExpanded = isConversationPanelExpanded,
            onToggleExpanded = { isConversationPanelExpanded = !isConversationPanelExpanded },
            modifier =
                    Modifier.align(Alignment.BottomStart)
                            .navigationBarsPadding()
                            .padding(bottom = 64.dp)
                            .zIndex(10f),
            panelWidth = 400.dp,
            panelColor = Color(0xFF394A59),
            contentPadding = PaddingValues(16.dp),
            heightFraction = 0.8f,
            tabPosition = 0.715f, // Position tab at the center of the panel
            sessionInfoList = sessionInfoList,
            onSessionSelected = { sessionId ->
              if (viewingSessionId == sessionId) {
                viewModel.returnToCurrentSession()
              } else {
                viewModel.loadSessionEntries(sessionId)
              }
            },
            onResumeSession = { sessionId -> viewModel.resumeSession(sessionId) },
            title = if (viewingSessionId == null) "Conversation History" else "Session History"
    )

    // Agent Process Panel with custom tab positioning
    AgentProcessPanel(
            recognitionState = recognitionState,
            dialogueState = dialogueState,
            conversationHistory = conversationHistory,
            currentPlan = currentPlan,
            currentAction = currentAction,
            isExpanded = isAgentPanelExpanded,
            onToggleExpanded = { isAgentPanelExpanded = !isAgentPanelExpanded },
            modifier =
                    Modifier.align(Alignment.TopEnd)
                            .padding(
                                    top = 56.dp,
                                    bottom = 56.dp
                            ) // Account for top and bottom spacing
                            .zIndex(10f),
            panelColor = backgroundBeige,
            borderColor = borderBlue,
            tabPosition = 0.25f, // Position tab 25% down from the top (was 10%)
            coreLoopState = coreLoopState, // Pass the core loop state
            checkInState = checkInState // Pass the check-in state for themes
    )

    /* TODO: Re-enable affirmation functionality post-MVP
     * agent overlay: the agent can display helper graphics during in depth conversations
     * if (agentDisplayState != DisplayState.None) {
     *   AffirmationOverlay(
     *           displayState = agentDisplayState,
     *           onStop = { agent.affirmationManager.stopCurrentSequence() }
     *   )
     * }
     */
  } // End Box
}

@Composable
fun VoxManifestorTitle(modifier: Modifier = Modifier) {

    val accentGold = Color(0xFFD4AF37) // Rich gold for accents
    val deepGold = Color(0xFF917600) // Rich gold for accents
    val deepPurple = Color(0xFF4A2545) // Deep purple for contrast

    // Title band with top and bottom borders only
    Box(
        modifier = modifier
            .background(
                color = accentGold.copy(alpha = 0.1f), // Very subtle gold background
                shape = RectangleShape
            )
            .border(width = 2.dp, color = accentGold, shape = RectangleShape)
            .drawBehind {
                // Draw top border
                drawLine(
                    color = Color.Black,
                    start = Offset(0f, 0f),
                    end = Offset(size.width, 0f),
                    strokeWidth = 1.dp.toPx()
                )
                // Draw bottom border
                drawLine(
                    color = Color.Black,
                    start = Offset(0f, size.height),
                    end = Offset(size.width, size.height),
                    strokeWidth = 1.dp.toPx()
                )
            }
            .padding(vertical = 12.dp), // Vertical padding only
        contentAlignment = Alignment.Center
    ) {
            // Row containing stars and title
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Spacer(modifier = Modifier.width(18.dp))

                Icon(
                    painter = painterResource(id = R.drawable.star),
                    contentDescription = null,
                    tint = deepGold,
                    modifier = Modifier.size(12.dp)
                )
                Icon(
                    painter = painterResource(id = R.drawable.star),
                    contentDescription = null,
                    tint = deepGold,
                    modifier = Modifier.size(12.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = "Vox Manifestor",
                    style =
                        MaterialTheme.typography.headlineMedium.copy(
                            color = deepPurple,
                            fontWeight = FontWeight.Bold,
                            fontFamily =
                                androidx.compose.ui.text.font.FontFamily
                                    .Monospace, // Techy monospace font
                            letterSpacing = 2.sp, // Enhanced letter spacing for sci-fi aesthetic
                            shadow =
                                Shadow(
                                    color = Color.Black.copy(alpha = 0.25f),
                                    offset = Offset(2f, 2f),
                                    blurRadius = 4f
                                )
                        ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(2f)
                )

                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    painter = painterResource(id = R.drawable.star),
                    contentDescription = null,
                    tint = deepGold,
                    modifier = Modifier.size(12.dp)
                )
                Icon(
                    painter = painterResource(id = R.drawable.star),
                    contentDescription = null,
                    tint = deepGold,
                    modifier = Modifier.size(12.dp)
                )

                Spacer(modifier = Modifier.width(18.dp))
            }
        }
    }

@Composable
fun VoxRecogZone(
        recognitionState: RecognitionState,
        commandState: VoiceCommandEntity,
        rawTextState: String,
        modifier: Modifier = Modifier
) {
  // Voice recognition status
  Column(modifier = modifier.fillMaxWidth()) {
    Card(
            modifier = Modifier.fillMaxWidth(),
            colors =
                    CardDefaults.cardColors(
                            containerColor = Color(0xFFE0E6FF) // Light gray background
                    ),
            border = BorderStroke(1.dp, Color(0xFF626262))
    ) {
      // Speech detection box with icon
      Row(
              modifier = Modifier.fillMaxWidth().height(70.dp).padding(horizontal = 12.dp),
              verticalAlignment = Alignment.CenterVertically
      ) {
        // Speech icon on the left
        Icon(
                painter = painterResource(id = R.drawable.speech),
                contentDescription = "Speech",
                modifier = Modifier.size(28.dp),
                tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.width(16.dp))

        // Text content
        Text(
                text = rawTextState,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.weight(1f).padding(vertical = 4.dp)
        )
      }

      // Removed the status messages section
    }
  }
}

@Composable
fun ManifestationItem(
        manifestation: Manifestation,
        onSelect: (Int, Boolean) -> Unit,
        isSelected: Boolean = false
) {
  val borderBlue = Color(0xFF3F51B5) // Indigo border for agent panel
  val accentGold = Color(0xFFD4AF37) // Rich gold for accents
  val deepPurple = Color(0xFF4A2545) // Deep purple for contrast
  val backgroundBeige = Color(0xFFF2EAD3) // Warm beige background
  val manifestationCardYellow = Color(0xFFFDDE81) // Original yellow card background

  Card(
          onClick = { onSelect(manifestation.slot, isSelected) },
          modifier = Modifier.fillMaxWidth().height(70.dp), // Reduced height to save vertical space
          colors =
                  CardDefaults.cardColors(
                          containerColor =
                                  manifestationCardYellow // Bring back the original yellow color
                  ),
          border =
                  BorderStroke(
                          width = if (isSelected) 3.dp else 1.dp,
                          color = if (isSelected) borderBlue else borderBlue.copy(alpha = 0.6f)
                  ),
          shape = RectangleShape, // Sharp edges for techy appearance
          elevation = CardDefaults.cardElevation(defaultElevation = if (isSelected) 4.dp else 2.dp)
  ) {
    Box(modifier = Modifier.fillMaxSize().padding(8.dp)) {
      // Background number with techy styling
      Text(
              text = "${manifestation.slot + 1}",
              color = deepPurple.copy(alpha = 0.3f), // More subtle background number
              style =
                      MaterialTheme.typography.displayLarge.copy(
                              fontWeight = FontWeight.ExtraBold,
                              fontFamily =
                                      androidx.compose.ui.text.font.FontFamily
                                              .Monospace // Techy font
                      ),
              modifier = Modifier.align(Alignment.CenterEnd).padding(end = 8.dp)
      )

      // Wish text with previous font style - allow multiple lines
      Text(
              text = manifestation.title,
              modifier =
                      Modifier.fillMaxSize()
                              .padding(
                                      start = 8.dp,
                                      top = 8.dp,
                                      end = 48.dp,
                                      bottom = 8.dp
                              ) // More padding for better spacing
                              .align(Alignment.CenterStart),
              style =
                      MaterialTheme.typography.bodyLarge.copy(
                              fontWeight = FontWeight.Medium,
                              color = deepPurple
                      ),
              maxLines = 2, // Reduced to 2 lines to save vertical space
              overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
      )
    }
  }
}

@Composable
fun EmptyManifestationItem() {
    val borderBlue = Color(0xFF3F51B5) // Indigo border for agent panel
    val manifestationCardYellow = Color(0xFFFDDE81) // Original yellow card background

    Card(
        modifier = Modifier.fillMaxWidth().height(70.dp), // Match the height of filled cards
        colors =
            CardDefaults.cardColors(
                containerColor =
                    manifestationCardYellow.copy(
                        alpha = 0.5f
                    ) // Subtle yellow background
            ),
        border = BorderStroke(1.dp, borderBlue.copy(alpha = 0.4f)), // Subtle border
        shape = RectangleShape, // Sharp edges for techy appearance
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Box(modifier = Modifier.fillMaxSize().padding(8.dp)) {
            Text(
                "",
            )
        }
    }
}

